# 师傅端首页筛选功能修改测试

## 修改内容

### 1. 报价订单页面隐藏价格区间筛选
- **位置**: 筛选面板中的价格筛选项
- **修改**: 添加条件 `v-if="currentTab !== 1 && !(currentTab === 2 && currentHighValueType === 1)"`
- **效果**: 当用户切换到"报价订单"选项卡或"高价值"中的"报价订单"子菜单时，价格区间筛选选项将被隐藏
- **新增数据**: 添加 `currentHighValueType` 来跟踪当前选择的高价值子菜单类型

### 2. 高价值子菜单添加小红点
- **位置**: 高价值子菜单项
- **修改**: 
  - 在子菜单项中添加 `submenu-badge` 组件
  - 添加相应的CSS样式
- **效果**: 高价值子菜单的"一口价"和"报价订单"项将显示小红点，数量来自API返回的 `heightOnePriceCount` 和 `heightParityCount`

## 测试步骤

### 测试1: 价格筛选隐藏功能
1. 打开师傅端首页
2. 默认在"一口价"选项卡，点击"筛选"按钮
3. 验证可以看到"价格区间"筛选选项
4. 切换到"报价订单"选项卡
5. 点击"筛选"按钮
6. **预期结果**: 应该看不到"价格区间"筛选选项，只有"订单距离"和"分类筛选"
7. 切换到"高价值"选项卡
8. 点击"报价订单"子菜单
9. 点击"筛选"按钮
10. **预期结果**: 在高价值的报价订单中也应该看不到"价格区间"筛选选项

### 测试2: 高价值子菜单小红点
1. 打开师傅端首页
2. 点击"高价值"选项卡
3. **预期结果**: 应该显示子菜单，包含"一口价"和"报价订单"两个选项
4. 如果API返回的 `heightOnePriceCount` > 0，"一口价"选项应该显示小红点
5. 如果API返回的 `heightParityCount` > 0，"报价订单"选项应该显示小红点

## 代码修改详情

### 数据修改
```javascript
// 新增数据属性
currentHighValueType: null, // 当前选择的高价值子菜单类型：0=一口价，1=报价订单

// 修改selectHighValueSubMenu方法
selectHighValueSubMenu(subItem) {
  this.currentHighValueType = subItem.type; // 记录当前选择的高价值类型
  // ... 其他逻辑
}

// 修改switchTab方法
switchTab(index) {
  if (index !== 2) {
    this.currentHighValueType = null; // 重置高价值类型
  }
  // ... 其他逻辑
}
```

### 模板修改
```vue
<!-- 筛选面板价格筛选项添加条件 -->
<view class="filter-item-container" v-if="currentTab !== 1 && !(currentTab === 2 && currentHighValueType === 1)">
  <!-- 价格筛选内容 -->
</view>

<!-- 高价值子菜单添加小红点 -->
<view class="submenu-item" v-for="(item, index) in highValueSubMenu" :key="index">
  <text>{{ item.name }}</text>
  <view class="submenu-badge" v-if="item.badge && item.badge > 0">{{ item.badge }}</view>
</view>
```

### 样式修改
```scss
.submenu-item {
  position: relative; // 添加相对定位
  // ... 其他样式
}

.submenu-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  text-align: center;
  border-radius: 16rpx;
  padding: 0 8rpx;
  transform: scale(0.8);
}
```

## 注意事项
- 小红点的数据来源于API返回的 `updateTabBadges` 方法中的 `heightOnePriceCount` 和 `heightParityCount`
- 价格筛选的隐藏只影响UI显示，不影响其他筛选功能
- 修改保持了原有的代码结构和样式风格
