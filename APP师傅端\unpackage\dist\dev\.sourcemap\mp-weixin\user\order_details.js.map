{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_details.vue?b7fe", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_details.vue?8059", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_details.vue?1fcf", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_details.vue?ad8c", "uni-app:///user/order_details.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_details.vue?642d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_details.vue?0e04"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "show", "afterSalesShow", "afterSalesValue", "info", "list", "title", "desc", "infoList", "children", "id", "methods", "confirm", "uni", "icon", "setTimeout", "cancelOrder", "openAfterSales", "submitAfterSales", "orderId", "remark", "console", "call", "phoneNumber", "getInfo", "name", "value", "onUnload", "delta", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2Dh3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACAF;QACAG;MACA,GACA;QACAH;QACAG;MACA,GACA;QACAH;QACAG;MACA,GACA;QACAH;QACAG;MACA,GACA;QACAH;QACAG;MACA,EACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QAAAF;MAAA;QACAG;UACAC;UACAR;QACA;QACAO;QACAE;UACAF;QACA;MACA;IACA;IACAG;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACAL;UACAC;UACAR;QACA;QACA;MACA;MACA;MACA;QACAa;QACAC;MACA;QACAC;QACA;UACAR;YACAC;YACAR;UACA;QACA;UACAO;YACAC;YACAR;UACA;UACA;UACA;QACA;MAEA;QACAO;UACAC;UACAR;QACA;MACA;IACA;IACAgB;MACA;QACAT;UACAP;UACAQ;QACA;QACA;MACA;MACAD;QACAU;MACA;IACA;IACAC;MAAA;MACA;QACAH;QACA;QACA;UACA;YACAf;YACAC;UACA;QACA;QACA;UACA;YACAD;YACAC;UACA;QACA;UACA;YACAD;YACAC;UACA;QACA;QACA;UACAkB;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACA;UACAD;UACAC;QACA;QACA;UACAD;UACAC;QACA;QACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACAd;QACAe;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChPA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/order_details.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/order_details.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order_details.vue?vue&type=template&id=f98852ca&scoped=true&\"\nvar renderjs\nimport script from \"./order_details.vue?vue&type=script&lang=js&\"\nexport * from \"./order_details.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order_details.vue?vue&type=style&index=0&id=f98852ca&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f98852ca\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/order_details.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=template&id=f98852ca&scoped=true&\"", "var components\ntry {\n  components = {\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uSteps: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-steps/u-steps\" */ \"uview-ui/components/u-steps/u-steps.vue\"\n      )\n    },\n    uStepsItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-steps-item/u-steps-item\" */ \"uview-ui/components/u-steps-item/u-steps-item.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.info.coachInfo\n    ? _vm.$util.timestampToTime(_vm.info.createTime * 1000)\n    : null\n  var g1 = _vm.info.coachInfo\n    ? [-1, 1, -2, -3].includes(_vm.info.payType)\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.afterSalesShow = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<u-modal :show=\"show\" title=\"取消订单\" content='确认取消该订单吗' showCancelButton @cancel=\"show = false\"\n\t\t\t@confirm=\"confirm\"></u-modal>\n\t\t<u-modal :show=\"afterSalesShow\" title=\"申请售后\" showCancelButton @cancel=\"afterSalesShow = false\"\n\t\t\t@confirm=\"submitAfterSales\">\n\t\t\t<view class=\"after-sales-input\">\n\t\t\t\t<view class=\"\">\n\t\t\t\t\t<textarea v-model=\"afterSalesValue\" placeholder=\"请输入售后内容\"\n\t\t\t\t\t\tstyle=\" padding: 20rpx; border: 2rpx solid #E9E9E9; border-radius: 8rpx; writing-mode: horizontal-tb; text-align: left;\"></textarea>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-modal>\n\t\t<view class=\"header\" v-if=\"info.coachInfo\">\n\t\t\t<view class=\"top\">\n\t\t\t\t<view class=\"left\">\n\t\t\t\t\t<view class=\"\" style=\"display: flex;align-items: center;\">\n\t\t\t\t\t\t<view class=\"name\">{{ info.coachInfo.coachName }}</view>\n\t\t\t\t\t\t<view class=\"\"\n\t\t\t\t\t\t\tstyle=\"background-color: #fac21f;color: #fff;width: fit-content;padding: 5rpx 10rpx;font-size: 24rpx;margin-left: 20rpx;border-radius: 6rpx;\"\n\t\t\t\t\t\t\tv-if=\"info.coachInfo.label_name\">{{ info.coachInfo.label_name }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"time\">{{ $util.timestampToTime(info.createTime * 1000) }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"right\">\n\t\t\t\t\t<image :src=\"info.coachInfo.selfImg\" mode=\"\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"![-1, 1, -2, -3].includes(info.payType)\" class=\"bott\" @click=\"call\">\n\t\t\t\t<view class=\"box\"><uni-icons type=\"phone-filled\" size=\"16\" color=\"#fff\"></uni-icons></view>\n\t\t\t\t<text>打电话给师傅</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-if=\"info.payType === 7\" class=\"after-sales-btn\" @click=\"openAfterSales\">\n\t\t\t去售后\n\t\t</view>\n\t\t<view class=\"schedule\">\n\t\t\t<u-steps current=\"4\" direction=\"column\">\n\t\t\t\t<u-steps-item :title=\"item.title\" :desc=\"item.desc\" v-for=\"(item, index) in list\" :key=\"index\">\n\t\t\t\t\t<view class=\"slot-icon\" slot=\"icon\">\n\t\t\t\t\t\t<view class=\"\" style=\"border-radius: 50%;background-color:#00b26a;padding: 5rpx;\">\n\t\t\t\t\t\t\t<u-icon name=\"checkbox-mark\" color=\"#ffffff\" size=\"14\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</u-steps-item>\n\t\t\t</u-steps>\n\t\t</view>\n\t\t<view class=\"info\" v-for=\"(item, index) in infoList\" :key=\"index\">\n\t\t\t<view class=\"title\">{{ item.title }}</view>\n\t\t\t<view class=\"info_item\" v-for=\"(newItem, newIndex) in item.children\" :key=\"newIndex\">\n\t\t\t\t<view class=\"left\">{{ newItem.name }}</view>\n\t\t\t\t<view class=\"right\">{{ newItem.value }}</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"btn\" v-if=\"info.payType <= 1 && info.payType != -1\" @click=\"cancelOrder\">取消订单</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tshow: false,\n\t\t\tafterSalesShow: false,\n\t\t\tafterSalesValue: '',\n\t\t\tinfo: {},\n\t\t\tlist: [{\n\t\t\t\ttitle: '订单已生成',\n\t\t\t\tdesc: '预定成功，将尽快为主人派单'\n\t\t\t}],\n\t\t\tinfoList: [{\n\t\t\t\ttitle: '预约信息',\n\t\t\t\tchildren: []\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"服务信息\",\n\t\t\t\tchildren: []\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"费用明细\",\n\t\t\t\tchildren: []\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"优惠明细\",\n\t\t\t\tchildren: []\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"下单明细\",\n\t\t\t\tchildren: []\n\t\t\t}\n\t\t\t],\n\t\t\tid: ''\n\t\t}\n\t},\n\tmethods: {\n\t\tconfirm() {\n\t\t\tthis.$api.service.cancelOrder({ id: this.info.id }).then(res => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '取消成功'\n\t\t\t\t})\n\t\t\t\tuni.$emit('cancelOr')\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack()\n\t\t\t\t}, 1000)\n\t\t\t})\n\t\t},\n\t\tcancelOrder() {\n\t\t\tthis.show = true\n\t\t},\n\t\topenAfterSales() {\n\t\t\tthis.afterSalesShow = true\n\t\t},\n\t\tsubmitAfterSales() {\n\t\t\tif (!this.afterSalesValue.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请输入售后内容'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\t\t\t// Here you would typically call an API to submit the after-sales request\n\t\t\tthis.$api.service.submitAfterSales({\n\t\t\t\torderId: this.id,\n\t\t\t\tremark: this.afterSalesValue\n\t\t\t}).then(res => {\n\t\t\t\tconsole.log(res)\n\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '售后申请提交成功'\n\t\t\t\t\t})\n\t\t\t\t\tthis.afterSalesShow = false\n\t\t\t\t\tthis.afterSalesValue = ''\n\t\t\t\t}\n\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '提交失败，请重试'\n\t\t\t\t})\n\t\t\t})\n\t\t},\n\t\tcall() {\n\t\t\tif (!this.info.coachInfo.mobile || this.info.coachInfo.mobile.includes('*')) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无法拨打电话，号码不可用',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tuni.makePhoneCall({\n\t\t\t\tphoneNumber: this.info.coachInfo.mobile\n\t\t\t});\n\t\t},\n\t\tgetInfo() {\n\t\t\tthis.$api.service.orderdet(this.id).then(res => {\n\t\t\t\tconsole.log(res)\n\t\t\t\tthis.info = res\n\t\t\t\tif (this.info.coachInfo) {\n\t\t\t\t\tthis.list.push({\n\t\t\t\t\t\ttitle: '订单已派单',\n\t\t\t\t\t\tdesc: `订单交给${this.info.coachInfo.coachName}，将督促师傅尽快跟您联系`\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tif (this.info.payType == 7) {\n\t\t\t\t\tthis.list.push({\n\t\t\t\t\t\ttitle: '订单完成',\n\t\t\t\t\t\tdesc: `订单已完成`\n\t\t\t\t\t})\n\t\t\t\t} else if (this.info.payType == -1) {\n\t\t\t\t\tthis.list.push({\n\t\t\t\t\t\ttitle: '订单取消',\n\t\t\t\t\t\tdesc: `订单已取消`\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tthis.infoList[0].children = [{\n\t\t\t\t\tname: '预约时间',\n\t\t\t\t\tvalue: this.$util.timestampToTime(this.info.startTime * 1000)\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: '服务地址',\n\t\t\t\t\tvalue: this.info.addressInfo.address + this.info.addressInfo.addressInfo + this.info.addressInfo.houseNumber\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: '预约服务',\n\t\t\t\t\tvalue: this.info.orderGoods[0].goodsName\n\t\t\t\t}\n\t\t\t\t]\n\t\t\t\tthis.infoList[1].children = [{\n\t\t\t\t\tname: '服务类型',\n\t\t\t\t\tvalue: this.info.type == 0 ? '一口价' : '报价'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: '服务产品',\n\t\t\t\t\tvalue: this.info.orderGoods[0].goodsName\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: '服务师傅',\n\t\t\t\t\tvalue: this.info.coachInfo ? this.info.coachInfo.coachName : ''\n\t\t\t\t}\n\t\t\t\t]\n\t\t\t\tthis.infoList[2].children = [{\n\t\t\t\t\tname: '服务费用',\n\t\t\t\t\tvalue: this.info.payPrice + '元'\n\t\t\t\t}]\n\t\t\t\tthis.infoList[3].children = [{\n\t\t\t\t\tname: '优惠券',\n\t\t\t\t\tvalue: this.info.couponInfo ? '-' + this.info.couponInfo.discount + '元' : '无'\n\t\t\t\t}]\n\t\t\t\tthis.infoList[4].children = [{\n\t\t\t\t\tname: '订单号码',\n\t\t\t\t\tvalue: this.info.orderCode\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: '下单时间',\n\t\t\t\t\tvalue: this.$util.timestampToTime(this.info.createTime * 1000)\n\t\t\t\t}\n\t\t\t\t]\n\t\t\t})\n\t\t}\n\t},\n\tonUnload() {\n\t\tlet pageArr = getCurrentPages()\n\t\tlet length = pageArr.length\n\t\tif (pageArr[length - 2].route == \"/pages/order_success\") {\n\t\t\tuni.navigateBack({\n\t\t\t\tdelta: 9\n\t\t\t})\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tthis.id = options.id\n\t\tthis.getInfo()\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tbackground-color: #f8f8f8;\n\theight: 100vh;\n\toverflow: auto;\n\tpadding: 40rpx 30rpx;\n\n\t.header {\n\t\twidth: 690rpx;\n\t\theight: 274rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 24rpx 24rpx 24rpx 24rpx;\n\t\tpadding: 28rpx 36rpx;\n\n\t\t.top {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\tpadding-bottom: 42rpx;\n\t\t\tborder-bottom: 2rpx solid #E9E9E9;\n\n\t\t\t.left {\n\t\t\t\t.name {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t}\n\n\t\t\t\t.time {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.right {\n\t\t\t\timage {\n\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.bott {\n\t\t\theight: 100rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\ttext {\n\t\t\t\tmargin-left: 20rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #2E80FE;\n\t\t\t}\n\n\t\t\t.box {\n\t\t\t\twidth: 42rpx;\n\t\t\t\theight: 42rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t}\n\t\t}\n\t}\n\n\t.after-sales-btn {\n\t\twidth: 686rpx;\n\t\theight: 88rpx;\n\t\tfont-weight: 700;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 20rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #2E80FE;\n\t\tline-height: 88rpx;\n\t\ttext-align: center;\n\t\tmargin: 16rpx auto;\n\t}\n\n\t.after-sales-input {\n\t\tpadding: 20rpx;\n\t}\n\n\t.schedule {\n\t\twidth: 690rpx;\n\t\tpadding: 32rpx 36rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 24rpx 24rpx 24rpx 24rpx;\n\n\t\t.slot-icon {\n\t\t\timage {\n\t\t\t\twidth: 32rpx;\n\t\t\t\theight: 32rpx;\n\t\t\t}\n\t\t}\n\n\t\t::v-deep .u-steps-item__line {\n\t\t\tbackground-color: #000 !important;\n\t\t}\n\t}\n\n\t.info {\n\t\tmargin-top: 20rpx;\n\t\twidth: 690rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 24rpx 24rpx 24rpx 24rpx;\n\t\tpadding: 28rpx 36rpx;\n\n\t\t.title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #333333;\n\t\t\tmargin-bottom: 26rpx;\n\t\t}\n\n\t\t.info_item {\n\t\t\tdisplay: flex;\n\t\t\tpadding: 24rpx 0;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\tborder-top: 2rpx solid #E9E9E9;\n\n\t\t\t.left {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #ADADAD;\n\t\t\t}\n\n\t\t\t.right {\n\t\t\t\tmax-width: 500rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #333333;\n\t\t\t\ttext-align: right;\n\t\t\t}\n\t\t}\n\t}\n\n\t.btn {\n\t\tmargin: 0 auto;\n\t\tmargin-top: 40rpx;\n\t\twidth: 686rpx;\n\t\theight: 88rpx;\n\t\tbackground: #2E80FE;\n\t\tborder-radius: 44rpx 44rpx 44rpx 44rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #FFFFFF;\n\t\tline-height: 88rpx;\n\t\ttext-align: center;\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=style&index=0&id=f98852ca&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=style&index=0&id=f98852ca&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755734374144\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}