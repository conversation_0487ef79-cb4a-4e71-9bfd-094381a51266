@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-5a79bfc8 {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
}
.img.data-v-5a79bfc8 {
  width: 690rpx;
  margin: 20rpx auto;
  border-radius: 12rpx;
  overflow: hidden;
}
.location-bar.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #fff;
  margin: 0 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  font-size: 26rpx;
  color: #666;
}
.location-info.data-v-5a79bfc8 {
  flex: 1;
}
.location-text.data-v-5a79bfc8 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tabs-container.data-v-5a79bfc8 {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.tabs-header.data-v-5a79bfc8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
}
.custom-tabs.data-v-5a79bfc8 {
  display: flex;
  flex: 1;
}
.tab-item.data-v-5a79bfc8 {
  flex: 1;
  flex-direction: column;
  padding: 20rpx 0;
  text-align: center;
  position: relative;
  font-size: 28rpx;
  color: #666;
  transition: color 0.3s;
}
.tab-item.active.data-v-5a79bfc8 {
  color: #2E80FE;
  font-weight: 600;
}
.tab-item.active.data-v-5a79bfc8::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #2E80FE;
  border-radius: 2rpx;
}
.tab-badge.data-v-5a79bfc8 {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  min-width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  text-align: center;
  border-radius: 16rpx;
  padding: 0 8rpx;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
}
.high-value-submenu.data-v-5a79bfc8 {
  display: flex;
  justify-content: center;
  gap: 40rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-top: 1rpx solid #eee;
  -webkit-animation: slideDown-data-v-5a79bfc8 0.3s ease-out;
          animation: slideDown-data-v-5a79bfc8 0.3s ease-out;
}
.submenu-item.data-v-5a79bfc8 {
  padding: 12rpx 24rpx;
  background-color: #fff;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  border: 1rpx solid #ddd;
  transition: all 0.3s;
  position: relative;
}
.submenu-item.data-v-5a79bfc8:active {
  background-color: #2E80FE;
  color: #fff;
  border-color: #2E80FE;
}
.submenu-badge.data-v-5a79bfc8 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  text-align: center;
  border-radius: 16rpx;
  padding: 0 8rpx;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
}
@-webkit-keyframes slideDown-data-v-5a79bfc8 {
from {
    opacity: 0;
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideDown-data-v-5a79bfc8 {
from {
    opacity: 0;
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.filter-container-inline.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
}
.filter-container-inline .filter-toggle-btn.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  border: 1rpx solid #ddd;
  transition: all 0.3s;
}
.filter-container-inline .filter-toggle-btn.active.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
  border-color: #2E80FE;
}
.filter-icon.data-v-5a79bfc8 {
  font-size: 24rpx;
}
.submenu-filter-container.data-v-5a79bfc8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-top: 1rpx solid #eee;
  -webkit-animation: slideDown-data-v-5a79bfc8 0.3s ease-out;
          animation: slideDown-data-v-5a79bfc8 0.3s ease-out;
}
.filter-container-inline.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
}
.filter-container-inline .filter-toggle-btn.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  border: 1rpx solid #ddd;
  transition: all 0.3s;
}
.filter-container-inline .filter-toggle-btn.active.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
  border-color: #2E80FE;
}
.filter-container.data-v-5a79bfc8 {
  background-color: #fff;
  padding: 0 20rpx;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.filter-bar.data-v-5a79bfc8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}
.filter-left.data-v-5a79bfc8 {
  flex: 1;
}
.filter-right.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
}
.filter-toggle-btn.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
}
.filter-toggle-btn.active.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
}
.filter-icon.data-v-5a79bfc8 {
  font-size: 24rpx;
}
.filter-panel.data-v-5a79bfc8 {
  display: block;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-top: 1rpx solid #eee;
  -webkit-animation: slideDown-data-v-5a79bfc8 0.3s ease-out;
          animation: slideDown-data-v-5a79bfc8 0.3s ease-out;
  position: relative;
}
.filter-panel .filter-item-container.data-v-5a79bfc8 {
  display: inline-block;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  position: relative;
}
.filter-item-container.data-v-5a79bfc8 {
  flex: 1;
  display: flex;
  justify-content: center;
}
.filter-item.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #333;
  padding: 8rpx 20rpx;
  border-radius: 40rpx;
  transition: background-color 0.3s, color 0.3s;
}
.filter-item.active-filter-item.data-v-5a79bfc8 {
  background-color: #e6f0ff;
  color: #2E80FE;
}
.arrow.data-v-5a79bfc8 {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: #aaa;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.arrow.rotate.data-v-5a79bfc8 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.filter-dropdown.data-v-5a79bfc8 {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx;
  margin-top: 8rpx;
  z-index: 1000;
  -webkit-animation: fadeIn-data-v-5a79bfc8 0.3s ease-out;
          animation: fadeIn-data-v-5a79bfc8 0.3s ease-out;
}
@-webkit-keyframes fadeIn-data-v-5a79bfc8 {
from {
    opacity: 0;
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeIn-data-v-5a79bfc8 {
from {
    opacity: 0;
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.dropdown-content.data-v-5a79bfc8 {
  max-height: 60vh;
  overflow-y: auto;
}
.filter-section.data-v-5a79bfc8 {
  margin-bottom: 24rpx;
}
.section-title.data-v-5a79bfc8 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}
.option-list.data-v-5a79bfc8 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.option-item.data-v-5a79bfc8 {
  padding: 16rpx;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  transition: background-color 0.3s, color 0.3s;
}
.option-item.active.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
}
.custom-price-inputs.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
}
.custom-price-inputs input.data-v-5a79bfc8 {
  flex: 1;
  height: 60rpx;
  padding: 0 16rpx;
  border: none;
  background: transparent;
  font-size: 26rpx;
  color: #333;
  text-align: center;
}
.custom-price-inputs text.data-v-5a79bfc8 {
  color: #999;
  font-size: 26rpx;
  padding: 0 8rpx;
}
.distance-input > text.data-v-5a79bfc8 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}
.distance-input .distance-input-container.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 16rpx;
}
.distance-input .distance-input-container input.data-v-5a79bfc8 {
  flex: 1;
  height: 60rpx;
  font-size: 26rpx;
  color: #333;
  background: transparent;
  border: none;
}
.distance-input .distance-input-container .unit.data-v-5a79bfc8 {
  font-size: 26rpx;
  color: #666;
}
.distance-input .distance-hint.data-v-5a79bfc8 {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  text-align: right;
}
.filter-actions.data-v-5a79bfc8 {
  display: flex;
  gap: 16rpx;
  margin-top: 24rpx;
}
.filter-btn.data-v-5a79bfc8 {
  flex: 1;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: opacity 0.3s;
}
.filter-btn.reset.data-v-5a79bfc8 {
  background-color: #f8f9fa;
  color: #666;
}
.filter-btn.confirm.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
}
.quotation-counts.data-v-5a79bfc8 {
  margin: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.counts-header.data-v-5a79bfc8 {
  text-align: center;
  margin-bottom: 16rpx;
}
.counts-header .counts-title.data-v-5a79bfc8 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.counts-row.data-v-5a79bfc8 {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.count-item.data-v-5a79bfc8 {
  text-align: center;
}
.count-item .count-label.data-v-5a79bfc8 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.count-item .count-value.data-v-5a79bfc8 {
  font-size: 28rpx;
  font-weight: 600;
  color: #2E80FE;
}
.count-divider.data-v-5a79bfc8 {
  width: 1rpx;
  height: 40rpx;
  background-color: #eee;
}
.re_item.data-v-5a79bfc8 {
  margin: 20rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.top.data-v-5a79bfc8 {
  display: flex;
  gap: 20rpx;
}
.top image.data-v-5a79bfc8 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
}
.top .order.data-v-5a79bfc8 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.top .order .title.data-v-5a79bfc8 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.top .order .price.data-v-5a79bfc8 {
  font-size: 30rpx;
  font-weight: 600;
  color: #2E80FE;
}
.info.data-v-5a79bfc8 {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}
.info .address.data-v-5a79bfc8,
.info .tel.data-v-5a79bfc8 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}
.info .address .left.data-v-5a79bfc8,
.info .tel .left.data-v-5a79bfc8 {
  margin-right: 16rpx;
  padding-top: 4rpx;
}
.info .address .right.data-v-5a79bfc8,
.info .tel .right.data-v-5a79bfc8 {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}
.info .address .right .address_name.data-v-5a79bfc8,
.info .tel .right .address_name.data-v-5a79bfc8 {
  font-weight: 500;
}
.info .address .right .address_Info.data-v-5a79bfc8,
.info .tel .right .address_Info.data-v-5a79bfc8 {
  color: #666;
  margin-top: 4rpx;
}
.notes.data-v-5a79bfc8 {
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
.notes view.data-v-5a79bfc8 {
  color: #999;
  margin-bottom: 8rpx;
}
.order-stats.data-v-5a79bfc8 {
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}
.order-stats .stats-row.data-v-5a79bfc8 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
}
.order-stats .stats-row.data-v-5a79bfc8:last-child {
  margin-bottom: 0;
}
.order-stats .stat-item.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.order-stats .stat-item .stat-label.data-v-5a79bfc8 {
  font-size: 24rpx;
  color: #666;
}
.order-stats .stat-item .stat-value.data-v-5a79bfc8 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.btn.data-v-5a79bfc8 {
  margin-top: 20rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #2E80FE;
  border: 1rpx solid #2E80FE;
  transition: background 0.3s;
}
.btn[style*="background-color"].data-v-5a79bfc8 {
  background-color: #2E80FE !important;
  color: #fff !important;
  border: none;
}
.loadmore.data-v-5a79bfc8 {
  padding: 20rpx 0;
  text-align: center;
}
.modal-content.data-v-5a79bfc8 {
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
  font-size: 26rpx;
  line-height: 1.6;
  color: #333;
}
/* 技能上传提示长条样式 */
.skill-upload-bar.data-v-5a79bfc8 {
  position: fixed;
  bottom: 120rpx;
  left: 20rpx;
  right: 20rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  z-index: 999;
  -webkit-animation: slideUp-data-v-5a79bfc8 0.3s ease-out;
          animation: slideUp-data-v-5a79bfc8 0.3s ease-out;
}
.skill-upload-content.data-v-5a79bfc8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.skill-upload-text.data-v-5a79bfc8 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}
.skill-upload-title.data-v-5a79bfc8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}
.skill-upload-subtitle.data-v-5a79bfc8 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}
.skill-upload-arrow.data-v-5a79bfc8 {
  margin-left: 20rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.skill-upload-arrow text.data-v-5a79bfc8 {
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
}
@-webkit-keyframes slideUp-data-v-5a79bfc8 {
from {
    opacity: 0;
    -webkit-transform: translateY(100rpx);
            transform: translateY(100rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp-data-v-5a79bfc8 {
from {
    opacity: 0;
    -webkit-transform: translateY(100rpx);
            transform: translateY(100rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}

