{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_list.vue?4639", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_list.vue?7f78", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_list.vue?6862", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_list.vue?1aaa", "uni-app:///user/order_list.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_list.vue?b615", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_list.vue?e17f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "status", "showConfirm", "showCancel", "showPaymentModal", "showRefundModal", "showRefundDetailsModal", "currentItem", "refundDetails", "<PERSON><PERSON><PERSON><PERSON>", "reminddata", "paymentRemind", "huodong<PERSON>", "isFromTiaozhuan", "isFromCartPlay", "tmplIds", "list", "name", "value", "currentIndex", "page", "orderList", "pay_typeArr", "id", "isLoading", "onPullDownRefresh", "uni", "onReachBottom", "setTimeout", "payType", "pageNum", "pageSize", "item", "console", "methods", "getCurrentPlatform", "handleAppWechatPay", "orderInfo", "appid", "noncestr", "package", "partnerid", "prepayid", "timestamp", "sign", "title", "icon", "url", "<PERSON><PERSON><PERSON><PERSON>", "handleMiniProgramPay", "timeStamp", "nonceStr", "signType", "paySign", "appId", "success", "fail", "handleAppWechatPayForDiff", "handleMiniProgramPayForDiff", "getDiffStatusText", "payDiffPrice", "type", "confirmDiffPrice", "content", "confirmText", "cancelText", "rejectDiffPrice", "editable", "placeholderText", "updateDiffStatus", "params", "previewImage", "urls", "current", "huo<PERSON>click", "gohuodongevaluate", "<PERSON><PERSON><PERSON>", "confirmPayment", "huodongwanchengclick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dingyue", "templateId", "templateCategoryId", "selectedTmplIds", "updateHighlight", "userId", "role", "goevaluate", "applyT", "confirmRefund", "viewRefundDetails", "refundStatusText", "goChoose", "confirmorder", "confirmconfirm", "orderId", "duration", "confirmCancel", "cancelorder", "goUrl", "handleOrderClick", "getList", "huodongRes", "userOrderRes", "Array", "handleHeader", "getcommissionRatio", "onLoad", "onShow", "onBackPress", "onUnload", "watch"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5MA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmP72B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,UACA,IACA,IACA,8CACA,8CACA;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;MACAC;IACA;MACAA;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;MACA;QACA;QACA;QACA;UAAA,uCACAC;YACAH;UAAA;QAAA,CACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACAI;MACA;IACA;EACA;EACAC;IACA;IACAC;MAKA;MAKA;IACA;IAEA;IACAC;MAAA;MACAH;MACAP;QACA;QACAW;MAAA,mEACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,kEACA;QACAX;QACAP;UACAmB;UACAC;QACA;QACA;QACAlB;UACAF;YACAqB;UACA;QACA;MACA,+DACA;QACAd;QACA;UACAP;YACAmB;YACAC;UACA;QACA;UACApB;YACAmB;YACAC;UACA;QACA;MACA,yBACA;IACA;IACAE;MACAtB;QACAqB;MACA;IACA;IACA;IACAE;MAAA;MACA;QACAC;QAAA;QACAC;QACAX;QACAY;QACAC;MACA;MACApB;MACAP;QACA;QACAwB;QACAC;QACAX;QACAC;QACAW;QACAC;QACAC;QACAC;UACA;UACAtB;UACAP;YACAmB;YACAC;UACA;UACA;UACAlB;YACAF;cACAqB;YACA;UACA;QACA;QACAS;UACA;UACAvB;UACAA;UACA;YACAP;cACAmB;cACAC;YACA;UACA;YACApB;cACAmB;cACAC;YACA;UACA;UACAb;UACAP;YACAmB;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAW;MAAA;QAAA;MACA/B;QACA;QACAW;MAAA,oEACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,mEACA;QACAX;QACAP;UACAmB;UACAC;QACA;QACA;QACA;QACA;MACA,gEACA;QACAb;QACA;UACAP;YACAmB;YACAC;UACA;QACA;UACApB;YACAmB;YACAC;UACA;QACA;MACA,0BACA;IACA;IAEA;IACAY;MAAA;MACA;QACAR;QAAA;QACAC;QACAX;QACAY;QACAC;MACA;MACApB;MACAP;QACA;QACAwB;QACAC;QACAX;QACAC;QACAW;QACAC;QACAC;QACAC;UACA;UACAtB;UACAP;YACAmB;YACAC;UACA;UACA;UACA;UACA;QACA;QACAU;UACA;UACAvB;UACAA;UACA;YACAP;cACAmB;cACAC;YACA;UACA;YACApB;cACAmB;cACAC;YACA;UACA;UACAb;UACAP;YACAmB;YACAC;UACA;QACA;MACA;IACA;IACA;IACAa;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACArC;QACAsC;MACA;QACA;UAEA5B;UACA;UACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACA;UACAA;;UAEA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;QAKA;UACAP;YACAmB;YACAC;UACA;QACA;MACA;QACApB;UACAmB;UACAC;QACA;QACAb;MACA;IACA;IAEA;IACA6B;MAAA;MACApC;QACAmB;QACAkB;QACAC;QACAC;QACAV;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAW;MAAA;MACAxC;QACAmB;QACAkB;QACAI;QACAC;QACAJ;QACAC;QACAV;UACA;YACA;YACA;cACA7B;gBACAmB;gBACAC;cACA;cACA;YACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAuB;MAAA;MAAA;MACA;QACA9C;QACAtB;MACA;MACA;QACAqE;MACA;MAEA;QACA;UACA5C;YACAmB;YACAC;UACA;UACA;UACA;UACA;QACA;UACApB;YACAmB;YACAC;UACA;QACA;MACA;QACApB;UACAmB;UACAC;QACA;QACAb;MACA;IACA;IAEA;IACAsC;MACA7C;QACA8C;QACAC;MACA;IACA;IAMAC;MACAhD;QACAoB;QACAD;MACA;IACA;IACA8B;MACAjD;QACAqB;MACA;IACA;IACA6B;MACA3C;MACA;MACA;IACA;IACA4C;MACA;MACA;QACAnD;UACAqB;QACA;MACA;IACA;IACA+B;MAAA;MACApD;QACAmB;QACAkB;QACAC;QACAC;QACAV;UACA;YACA;cACAhC;YACA;cACA;gBACAG;kBACAoB;kBACAD;gBACA;gBACA;gBACA;gBACA;cACA;gBACAnB;kBACAoB;kBACAD;gBACA;cACA;YACA;cACAnB;gBACAoB;gBACAD;cACA;cACAZ;YACA;UACA;QACA;MACA;IACA;IACA8C;MAAA;MACA;MACA;QACAxD;MACA;QACAG;UACAoB;UACAD;QACA;QACA;QACA;QACA;MACA;QACAnB;UACAoB;UACAD;QACA;QACAZ;MACA;IACA;IACA+C;MAAA;MACA;MACA;MACA;QACA/C;QACA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACA;QAAA;UACAgD;UACAC;QACA;MAAA;MACAxD;QACAX;QACAwC;UACA;UACA4B;YACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;gBACA;cACA;YACA;UACA;QACA;QACA3B;MACA;IACA;IACA4B;MACA;MACA;QACAnD;QACA;MACA;MACA;QACAoD;QACAC;QACAzD;MACA;QACAI;MACA;QACAA;MACA;IACA;IACAsD;MACA7D;QACAqB;MACA;IACA;IACAyC;MACA;MACA;IACA;IACAC;MACA;MACA;QACA/D;UACAqB;QACA;MACA;IACA;IACA;IACA2C;MAAA;MACA;QACAnE;MACA;QACA;UACA;UACA;QACA;UACAG;YACAoB;YACAD;UACA;QACA;MACA;QACAnB;UACAoB;UACAD;QACA;QACAZ;MACA;IACA;IACA;IACA0D;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IACAC;MACA3D;MACA;MACAP;QACAqB;MACA;IACA;IACA8C;MACA;MACA;IACA;IACAC;MACA;MACA;QACAC;MACA;QACA;UACArE;YACAoB;YACAD;YACAmD;UACA;QACA;UACAtE;YACAoB;YACAD;UACA;UACAnB;YACAqB;UACA;QACA;MACA;QACArB;UACAoB;UACAD;QACA;QACAZ;MACA;IACA;IACAgE;MAAA;MACA;MACA;QACA1E;MACA;QACAG;UACAoB;UACAD;QACA;QACA;QACA;QACA;MACA;QACAnB;UACAoB;UACAD;QACA;QACAZ;MACA;IACA;IACAiE;MACA;MACA;IACA;IACAC;MACAzE;QACAqB;MACA;IACA;IACAqD;MACA;MACA;QACA1E;UACAqB;QACA;MACA;QACA;QACArB;UACAqB;QACA;MACA;MACA;IACA;IACAsD;MAAA;MACA;MACApE;;MAEA;MACAP;QACAmB;MACA;MAEA;QACA,oBACA,kCACA;UACAhB;UACAC;UACAC;QACA,GACA;UAAA;YAAAuE;YAAAC;UACAtE;UACAA;UAEA,iEACAuE;YAAA;UAAA,MACAF,gBACA;UACA;UAEA;UACA;YAAA,uCACAtE;cACAH;YAAA;UAAA,CACA;UAEA;UACAI;;UAEA;UACA;UACA;UAEAA;UACAA;UAEAP;;UAEA;UACA;QAEA;UACAA;UACAA;YACAoB;YACAD;UACA;UACAZ;UACA;UACA;UACA;QACA;MACA;QACA;QACA;UACAJ;UACAC;UACAC;QACA;UACAE;UACA;UACA;UACA;YAAA,uCACAD;cACAH;YAAA;UAAA,CACA;UAEAI;;UAEA;UACA;UACA;UAEAA;UACAA;UAEAP;;UAEA;UACA;QAEA;UACAA;UACAA;YACAoB;YACAD;UACA;UACAZ;UACA;UACA;QACA;MACA;IACA;IACAwE;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;QACAzE;MACA;IACA;EACA;EACA0E;IAAA;IACA1E;IACA;MACA;MACAA;IACA;MACA;MACAA;IACA;MACA;MACA;MACAA;IACA;;IAEA;IACA;MACA;QACA;QACA;MACA;IACA;MACAA;IACA;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;MACAL;QACAK;QACA;UACAA;QACA;UACAA;QACA;MACA;IACA;IAEA;EACA;EACA2E;IAAA;IACA3E;IAEAP;MACA;MACA;MACA;IACA;;IAEA;IACA;IACA;IACA;IAEAO;IACAA;;IAEA;IACA;MACAA;MACA;MACA;MACA;MACA;QACAA;MACA;MACA;IACA;;IAEA;IACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;EACA;EACA4E;IACA;MACA;MACAnF;QACAqB;MACA;IACA;MACA;MACArB;QACAqB;MACA;IACA;IACA;EACA;EACA+D;IACA;MACApF;QACAqB;MACA;IACA;MACA;MACArB;QACAqB;MACA;IACA;EACA;EACAgE;IACA5F;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChrCA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/order_list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/order_list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order_list.vue?vue&type=template&id=1ee6bdd6&scoped=true&\"\nvar renderjs\nimport script from \"./order_list.vue?vue&type=script&lang=js&\"\nexport * from \"./order_list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order_list.vue?vue&type=style&index=0&id=1ee6bdd6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1ee6bdd6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/order_list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=template&id=1ee6bdd6&scoped=true&\"", "var components\ntry {\n  components = {\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.orderList.length\n  var g1 = _vm.orderList.length\n  var g2 = g1 === 0 ? _vm.orderList.length : null\n  var l2 = _vm.__map(_vm.orderList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 =\n      item.payType >= -1 && !(item.payType === -1)\n        ? parseInt(item.payType)\n        : null\n    var m1 =\n      item.payType >= -1\n        ? item.payType === 0 ||\n          (item.payType === 1 && parseInt(item.payType) >= 1)\n        : null\n    var m2 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 1 && parseInt(item.type) !== 5\n        : null\n    var m3 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 1 && parseInt(item.type) === 5\n        : null\n    var m4 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 1 && parseInt(item.type) === 5\n        : null\n    var m5 =\n      item.payType >= -1\n        ? parseInt(item.payType) >= 2 &&\n          parseInt(item.payType) !== 7 &&\n          parseInt(item.type) !== 5\n        : null\n    var m6 =\n      item.payType >= -1\n        ? parseInt(item.payType) >= 2 &&\n          parseInt(item.payType) !== 7 &&\n          parseInt(item.payType) !== 6 &&\n          parseInt(item.type) === 5\n        : null\n    var m7 =\n      item.payType >= -1\n        ? parseInt(item.payType) >= 2 &&\n          parseInt(item.payType) !== 7 &&\n          parseInt(item.payType) !== 6 &&\n          parseInt(item.type) === 5\n        : null\n    var m8 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 7 &&\n          item.isComment === 0 &&\n          item.type === 5\n        : null\n    var m9 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 7 &&\n          item.isComment === 1 &&\n          item.type === 5\n        : null\n    var m10 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 7 &&\n          item.isComment === 0 &&\n          item.type !== 5\n        : null\n    var m11 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 7 &&\n          item.isComment === 1 &&\n          item.type !== 5\n        : null\n    var g3 =\n      item.payType >= -1\n        ? item.orderDiffPrices && item.orderDiffPrices.length > 0\n        : null\n    var l0 =\n      item.payType >= -1 && g3\n        ? _vm.__map(item.orderDiffPrices, function (diffItem, diffIndex) {\n            var $orig = _vm.__get_orig(diffItem)\n            var m12 = _vm.getDiffStatusText(diffItem.status)\n            return {\n              $orig: $orig,\n              m12: m12,\n            }\n          })\n        : null\n    var g4 = !(item.payType >= -1) ? item.quotedPriceVos.length : null\n    var g5 = !(item.payType >= -1) ? item.quotedPriceVos.length : null\n    var l1 = !(item.payType >= -1)\n      ? _vm.__map(item.quotedPriceVos, function (shfItem, shfIndex) {\n          var $orig = _vm.__get_orig(shfItem)\n          var g6 = (shfItem.price * (1 + _vm.jiaNum / 100)).toFixed(2)\n          return {\n            $orig: $orig,\n            g6: g6,\n          }\n        })\n      : null\n    var g7 = !(item.payType >= -1) ? item.quotedPriceVos.length : null\n    var g8 = !(item.payType >= -1) && g7 > 0 ? item.quotedPriceVos.length : null\n    var m13 = !(item.payType >= -1)\n      ? item.payType === -3 && parseInt(item.type) !== 5\n      : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      m5: m5,\n      m6: m6,\n      m7: m7,\n      m8: m8,\n      m9: m9,\n      m10: m10,\n      m11: m11,\n      g3: g3,\n      l0: l0,\n      g4: g4,\n      g5: g5,\n      l1: l1,\n      g7: g7,\n      g8: g8,\n      m13: m13,\n    }\n  })\n  var m14 = _vm.refundStatusText(_vm.refundDetails.status)\n  var g9 = _vm.orderList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCancel = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showConfirm = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showPaymentModal = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showRefundModal = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.showRefundDetailsModal = false\n    }\n    _vm.e5 = function ($event) {\n      _vm.showRefundDetailsModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l2: l2,\n        m14: m14,\n        g9: g9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=script&lang=js&\"", "```html\n<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header_item\" v-for=\"(item, index) in list\" :key=\"index\" @click=\"handleHeader(item)\">\n\t\t\t\t<view :style=\"currentIndex === item.value ? 'color:#2E80FE;' : ''\">{{ item.name }}</view>\n\t\t\t\t<view class=\"blue\" :style=\"currentIndex === item.value ? '' : 'background-color:#fff;'\"></view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<u-empty mode=\"order\" icon=\"http://cdn.uviewui.com/uview/empty/order.png\" v-if=\"orderList.length === 0\">\n\t\t</u-empty>\n\n\t\t<!-- 调试信息 -->\n\t\t<view v-if=\"orderList.length === 0\" style=\"text-align: center; padding: 20rpx; color: #999;\">\n\t\t\t当前订单数量: {{ orderList.length }}\n\t\t</view>\n\n\t\t<view @click=\"dingyue()\" class=\"main\">\n\t\t\t<view v-for=\"(item, index) in orderList\" :key=\"index\">\n\t\t\t\t<view class=\"main_item\" v-if=\"item.payType >= -1\"\n\t\t\t\t\t@click=\"handleOrderClick(item)\">\n\t\t\t\t\t<view class=\"head\">\n\t\t\t\t\t\t<view class=\"no\">单号：{{ item.orderCode }}</view>\n\t\t\t\t\t\t<view class=\"type\">{{ item.payType === -1 ? '已取消' : pay_typeArr[parseInt(item.payType)] }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"order-type-info\">\n\t\t\t\t\t\t<text class=\"order-type-label\">报价类型：</text>\n\t\t\t\t\t\t<text class=\"order-type-value\">{{ item.type === 0 ? '一口价模式' : item.type === 1 ? '报价模式' : '未知类型'\n\t\t\t\t\t\t\t}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"mid\">\n\t\t\t\t\t\t<view class=\"lef\">\n\t\t\t\t\t\t\t<image :src=\"item.goodsCover\" mode=\"\"></image>\n\t\t\t\t\t\t\t<text>{{ item.goodsName }}</text>\n\t\t\t\t\t\t\t<text style=\"color:#F60100 ;\" v-if=\"item.type === 5\">活动订单</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"righ\"\n\t\t\t\t\t\t\tv-if=\"item.payType === 0 || (item.payType === 1 && parseInt(item.payType) >= 1)\">\n\t\t\t\t\t\t\t<view>￥{{ item.payPrice }}</view>\n\t\t\t\t\t\t\t<view>x{{ item.num ? item.num : 1 }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t\t<text>{{ item.createTime }}</text>\n\t\t\t\t\t\t<view class=\"qzf\" v-if=\"parseInt(item.payType) === 1 && parseInt(item.type) !== 5\"\n\t\t\t\t\t\t\************=\"gozhifua(`/user/Cashier?id=${item.id}&price=${item.payPrice}&type=${item.payType}&goodsId=${item.goodsId}`)\">\n\t\t\t\t\t\t\t去支付\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"qzf\" v-if=\"parseInt(item.payType) === 1 && parseInt(item.type) === 5\"\n\t\t\t\t\t\t\************=\"goUrl(`/user/huodongCashier?id=${item.id}&price=${item.payPrice}&type=${item.payType}&goodsId=${item.goodsId}`)\">\n\t\t\t\t\t\t\t去支付\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qzf\" v-if=\"parseInt(item.payType) === 1 && parseInt(item.type) === 5\"\n\t\t\t\t\t\t\************=\"huodongquxiaos(item)\">\n\t\t\t\t\t\t\t取消订单\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qrwc\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7 && parseInt(item.type) !== 5\"\n\t\t\t\t\t\t\************=\"confirmorder(item)\">\n\t\t\t\t\t\t\t确认完成\n\t\t\t\t\t\t</view>\n\t\t\t\t<!-- \t\t<view class=\"qrwc\"\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\************=\"confirmorder(item)\">\n\t\t\t\t\t\t\t确认完成\n\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t<view class=\"qrwc\" v-if=\"item.payType >= 2 && item.payType < 7 && item.refundStatus === 0\"\n\t\t\t\t\t\t\************=\"applyT(item)\">\n\t\t\t\t\t\t\t申请退款\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qrwc\" v-if=\"item.payType >= 1 && item.payType < 7 && item.refundStatus !== 0\"\n\t\t\t\t\t\t\************=\"viewRefundDetails(item)\">\n\t\t\t\t\t\t\t查看退款详情\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qrwc\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7 && parseInt(item.payType) !== 6 && parseInt(item.type) === 5\"\n\t\t\t\t\t\t\************=\"huodongwanchengclick(item)\">\n\t\t\t\t\t\t\t确认完成\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"color: #999999; background-color: #f0f0f0;\" class=\"qrwc\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7 && parseInt(item.payType) !== 6 && parseInt(item.type) === 5\"\n\t\t\t\t\t\t\************=\"huodongclick()\">\n\t\t\t\t\t\t\t待上门\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qpl\" v-if=\"parseInt(item.payType) === 7 && item.isComment === 0 && item.type === 5\"\n\t\t\t\t\t\t\************=\"gohuodongevaluate(item)\">去评价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qpl\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) === 7 && item.isComment === 1 && item.type === 5\">\n\t\t\t\t\t\t\t已评价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qpl\" v-if=\"parseInt(item.payType) === 7 && item.isComment === 0 && item.type !== 5\"\n\t\t\t\t\t\t\************=\"goevaluate(item)\">去评价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qpl\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) === 7 && item.isComment === 1 && item.type !== 5\">\n\t\t\t\t\t\t\t已评价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 子订单（差价申请列表） -->\n\t\t\t\t\t<view v-if=\"item.orderDiffPrices && item.orderDiffPrices.length > 0\" class=\"sub_orders\">\n\t\t\t\t\t\t<view class=\"sub_title\">差价申请记录</view>\n\t\t\t\t\t\t<view class=\"sub_item\" v-for=\"(diffItem, diffIndex) in item.orderDiffPrices\" :key=\"diffItem.id\"\n\t\t\t\t\t\t\************=\"\">\n\t\t\t\t\t\t\t<view class=\"sub_head\">\n\t\t\t\t\t\t\t\t<view class=\"sub_no\">差价单号：{{ diffItem.diffCode }}</view>\n\t\t\t\t\t\t\t\t<view class=\"sub_status\">{{ getDiffStatusText(diffItem.status) }}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"sub_content\">\n\t\t\t\t\t\t\t\t<view class=\"sub_info\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub_amount\">差价金额：￥{{ diffItem.diffAmount }}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"sub_reason\">原因：{{ diffItem.reasonDetail }}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"sub_time\">申请时间：{{ diffItem.createdTime }}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"sub_parts\" v-if=\"diffItem.partsImgs\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub_parts_label\">配件图片：</text>\n\t\t\t\t\t\t\t\t\t\t<image class=\"sub_parts_img\" :src=\"diffItem.partsImgs\" mode=\"aspectFit\"\n\t\t\t\t\t\t\t\t\t\t\t@click=\"previewImage(diffItem.partsImgs)\"\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"width: 120rpx; height: 120rpx; border-radius: 8rpx; margin-left: 10rpx;\">\n\t\t\t\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"sub_actions\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub_qzf\" v-if=\"diffItem.status === 0\"\n\t\t\t\t\t\t\t\t\t\************=\"confirmDiffPrice(diffItem)\">\n\t\t\t\t\t\t\t\t\t\t确认差价\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"sub_qzf sub_reject\" v-if=\"diffItem.status === 0\"\n\t\t\t\t\t\t\t\t\t\************=\"rejectDiffPrice(diffItem)\">\n\t\t\t\t\t\t\t\t\t\t拒绝差价\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"sub_qzf\" v-if=\"diffItem.status === 1\"\n\t\t\t\t\t\t\t\t\t\************=\"payDiffPrice(diffItem)\">\n\t\t\t\t\t\t\t\t\t\t去支付\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"main_item_already\" v-else @click=\"goChoose(item)\">\n\t\t\t\t\t<view style=\"font-size: 32rpx;font-weight: 500;\" class=\"title\">\n\t\t\t\t\t\t{{ item.quotedPriceVos.length === 0 ? '等待师傅报价' : '等待您选择师傅' }}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"ok\" v-if=\"item.quotedPriceVos.length > 0\">已有师傅报价</view>\n\t\t\t\t\t<view class=\"no\">单号：{{ item.orderCode }}</view>\n\t\t\t\t\t<view class=\"mid\">\n\t\t\t\t\t\t<view class=\"lef\">\n\t\t\t\t\t\t\t<image :src=\"item.goodsCover\" mode=\"\"></image>\n\t\t\t\t\t\t\t<text>{{ item.goodsName }}</text>\n\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text style=\"\">数量:{{ item.num }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t\t<text>{{ item.createTime }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"shifu\">\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\">\n\t\t\t\t\t\t\t<view class=\"shifu_item\" v-for=\"(shfItem, shfIndex) in item.quotedPriceVos\" :key=\"shfIndex\">\n\t\t\t\t\t\t\t\t<image :src=\"shfItem.selfImg ? shfItem.selfImg : '/static/mine/default_user.png'\"\n\t\t\t\t\t\t\t\t\tmode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t<text>￥{{ (shfItem.price * (1 + jiaNum / 100)).toFixed(2) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"item.quotedPriceVos.length > 0\"\n\t\t\t\t\t\tstyle=\"display: flex; justify-content: center; align-items: center; margin-top: 20rpx;\">\n\t\t\t\t\t\t<view class=\"qxdd\" @click.stop=\"cancelorder(item)\">取消订单</view>\n\t\t\t\t\t\t<view style=\"margin-left: 20%;\" class=\"tips\" vif=\"item.quotedPriceVos.length > 0\">\n\t\t\t\t\t\t\t{{ item.quotedPriceVos.length }}位师傅已报价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qxdd\" style=\"margin-left: 20rpx;\">\n\t\t\t\t\t\t\t选择师傅\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"item.payType === -3 && parseInt(item.type) !== 5\" class=\"qxdd\"\n\t\t\t\t\t\************=\"cancelorder(item)\">取消订单</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<u-modal :show=\"showCancel\" title=\"取消订单\" content=\"确认要取消该订单吗\" showCancelButton @cancel=\"showCancel = false\"\n\t\t\t@confirm=\"confirmCancel\"></u-modal>\n\t\t<u-modal :show=\"showConfirm\" title=\"完成订单\" content=\"确认要完成该订单吗\" showCancelButton @cancel=\"showConfirm = false\"\n\t\t\t@confirm=\"confirmconfirm\"></u-modal>\n\n\t\t<u-modal :show=\"showPaymentModal\" title=\"提示\" showCancelButton confirm-text=\"去支付\"\n\t\t\t@cancel=\"showPaymentModal = false\" @confirm=\"confirmPayment\">\n\n\t\t\t<view class=\"modal-content-red\">\n\t\t\t\t{{ paymentRemind }}\n\t\t\t</view>\n\n\t\t</u-modal>\n\n\t\t<u-modal :show=\"showRefundModal\" title=\"提示\" showCancelButton @cancel=\"showRefundModal = false\"\n\t\t\t@confirm=\"confirmRefund\">\n\t\t\t<view class=\"modal-content-red\">\n\t\t\t\t{{ reminddata }}\n\t\t\t</view>\n\t\t</u-modal>\n\n\t\t<u-modal :show=\"showRefundDetailsModal\" title=\"退款详情\" @cancel=\"showRefundDetailsModal = false\"\n\t\t\t@confirm=\"showRefundDetailsModal = false\" confirm-text=\"关闭\">\n\t\t\t<view class=\"modal-content-details\">\n\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t<text class=\"label\">退款单号:</text>\n\t\t\t\t\t<text class=\"value\">{{ refundDetails.orderCode }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t<text class=\"label\">申请时间:</text>\n\t\t\t\t\t<text class=\"value\">{{ refundDetails.createTime }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t<text class=\"label\">状态:</text>\n\t\t\t\t\t<text class=\"value\">{{ refundStatusText(refundDetails.status) }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"refundDetails.refundText\">\n\t\t\t\t\t<text class=\"label\">驳回备注:</text>\n\t\t\t\t\t<text class=\"value\">{{ refundDetails.refundText }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"refundDetails.refundTime\">\n\t\t\t\t\t<text class=\"label\">审核时间:</text>\n\t\t\t\t\t<text class=\"value\">{{ refundDetails.refundTime }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"refundDetails.outRefundNo\">\n\t\t\t\t\t<text class=\"label\">退款回执单号:</text>\n\t\t\t\t\t<text class=\"value\">{{ refundDetails.outRefundNo }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-modal>\n\n\t\t<view style=\"display: flex; justify-content: center;\" v-if=\"orderList.length >= 10\">\n\t\t\t<u-loadmore :status=\"status\" />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tstatus: 'loadmore',\n\t\t\tshowConfirm: false,\n\t\t\tshowCancel: false,\n\t\t\tshowPaymentModal: false,\n\t\t\tshowRefundModal: false,\n\t\t\tshowRefundDetailsModal: false,\n\t\t\tcurrentItem: null,\n\t\t\trefundDetails: {}, // New data property for refund details\n\t\t\tjiaNum: 0,\n\t\t\treminddata: '若你选择线下交易，无平台监管遭遇诈骗或者纠纷需由您自行承担损失！',\n\t\t\tpaymentRemind: '无平台担保的支付可能遭遇“假维修”“小病大修”等套路（据消协数，40%的线下维修投诉涉及虚报故障）',\n\t\t\thuodonglist: [],\n\t\t\tisFromTiaozhuan: false,\n\t\t\tisFromCartPlay: false,\n\t\t\ttmplIds: [\n\t\t\t\t'',\n\t\t\t\t'',\n\t\t\t\t'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihQv6I',\n\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t],\n\t\t\tlist: [{\n\t\t\t\tname: '全部',\n\t\t\t\tvalue: 0\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '报价列表',\n\t\t\t\tvalue: -2\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '待支付',\n\t\t\t\tvalue: 1\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '待服务',\n\t\t\t\tvalue: 5\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '服务中',\n\t\t\t\tvalue: 6\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '已完成',\n\t\t\t\tvalue: 7\n\t\t\t}\n\t\t\t],\n\t\t\tcurrentIndex: 0,\n\t\t\tpage: 1,\n\t\t\torderList: [],\n\t\t\tpay_typeArr: ['', '待支付', '报价列表', '已接单', '上门中', '待服务', '服务中', '详情>'],\n\t\t\tid: '',\n\t\t\tisLoading: false\n\t\t};\n\t},\n\tonPullDownRefresh() {\n\t\tthis.page = 1;\n\t\tthis.orderList = [];\n\t\tthis.status = 'loadmore';\n\t\tthis.getList(this.currentIndex).then(() => {\n\t\t\tuni.stopPullDownRefresh();\n\t\t}).catch(() => {\n\t\t\tuni.stopPullDownRefresh();\n\t\t});\n\t},\n\tonReachBottom() {\n\t\tif (this.status === 'nomore' || this.isLoading) return;\n\t\tthis.isLoading = true;\n\t\tthis.status = 'loading';\n\t\tthis.page++;\n\t\tsetTimeout(() => {\n\t\t\tthis.$api.service.userOrder({\n\t\t\t\tpayType: this.currentIndex,\n\t\t\t\tpageNum: this.page,\n\t\t\t\tpageSize: 10\n\t\t\t}).then(ress => {\n\t\t\t\tlet res = ress.data\n\t\t\t\tconst list = Array.isArray(res.list) ? res.list : [];\n\t\t\t\tconst normalizedList = list.map(item => ({\n\t\t\t\t\t...item,\n\t\t\t\t\tpayType: parseInt(item.payType)\n\t\t\t\t}));\n\t\t\t\tthis.orderList = [...this.orderList, ...normalizedList];\n\t\t\t\tthis.status = list.length < 10 ? 'nomore' : 'loadmore';\n\t\t\t\tthis.isLoading = false;\n\t\t\t}).catch(err => {\n\t\t\t\tthis.status = 'nomore';\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tthis.page--;\n\t\t\t\tconsole.error('Error loading more:', err);\n\t\t\t});\n\t\t}, 1500);\n\t},\n\tmethods: {\n\t\t// 检查当前平台\n\t\tgetCurrentPlatform() {\n\t\t\t// #ifdef APP-PLUS\n\t\t\treturn 'app-plus';\n\t\t\t// #endif\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\treturn 'mp-weixin';\n\t\t\t// #endif\n\t\t\t// #ifdef H5\n\t\t\treturn 'h5';\n\t\t\t// #endif\n\t\t\treturn 'unknown';\n\t\t},\n\n\t\t// APP微信支付处理\n\t\thandleAppWechatPay(obj) {\n\t\t\tconsole.log(111)\n\t\t\tuni.requestPayment({\n\t\t\t\t\"provider\": \"wxpay\",\n\t\t\t\torderInfo: 'orderInfo',\n\t\t\t\torderInfo: {\n\t\t\t\t\tappid: obj.appId,\n\t\t\t\t\tnoncestr: obj.nonceStr,\n\t\t\t\t\tpackage: 'Sign=WXPay',\n\t\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\t\tprepayid: obj.prepayId,\n\t\t\t\t\ttimestamp: String(obj.timestamp),\n\t\t\t\t\tsign: obj.sign\n\t\t\t\t},\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('APP微信支付成功', res);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t\t// this.dingyue()\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: '/user/order_list?tab=0'\n\t\t\t\t\t\t})\n\t\t\t\t\t}, 1000)\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('APP微信支付失败:', err);\n\t\t\t\t\tif (err.errMsg && err.errMsg.includes('cancel')) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tgozhifua(e) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: e\n\t\t\t});\n\t\t},\n\t\t// 微信小程序支付处理（保持原有逻辑）\n\t\thandleMiniProgramPay(obj) {\n\t\t\tconst paymentParams = {\n\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string\n\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\tsignType: 'MD5',\n\t\t\t\tpaySign: obj.sign\n\t\t\t};\n\t\t\tconsole.log(JSON.stringify(paymentParams));\n\t\t\tuni.requestPayment({\n\t\t\t\t\"provider\": 'wxpay',\n\t\t\t\ttimeStamp: String(obj.timestamp),\n\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\tsignType: \"MD5\",\n\t\t\t\tpaySign: obj.sign,\n\t\t\t\tappId: obj.appId,\n\t\t\t\tsuccess: (res1) => {\n\t\t\t\t\t// 支付成功回调\n\t\t\t\t\tconsole.log('支付成功', res1);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t\tthis.dingyue()\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: '/user/order_list?tab=0'\n\t\t\t\t\t\t})\n\t\t\t\t\t}, 1000)\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\t// 支付失败回调\n\t\t\t\t\tconsole.error('requestPayment fail object:', err);\n\t\t\t\t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\n\t\t\t\t\tif (err.errMsg.includes('fail cancel')) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tconsole.error('支付失败', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付失败请检查网络',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t})\n\t\t\t\t},\n\t\t\t})\n\t\t},\n\n\t\t// APP微信支付处理（差价支付专用）\n\t\thandleAppWechatPayForDiff(obj, diffItem) {\n\t\t\tuni.requestPayment({\n\t\t\t\t\"provider\": \"wxpay\",\n\t\t\t\torderInfo: 'orderInfo',\n\t\t\t\torderInfo: {\n\t\t\t\t\tappid: obj.appId,\n\t\t\t\t\tnoncestr: obj.nonceStr,\n\t\t\t\t\tpackage: 'Sign=WXPay',\n\t\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\t\tprepayid: obj.prepayId,\n\t\t\t\t\ttimestamp: String(obj.timestamp),\n\t\t\t\t\tsign: obj.sign\n\t\t\t\t},\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('APP微信支付成功', res);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t\tthis.page = 1;\n\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('APP微信支付失败:', err);\n\t\t\t\t\tif (err.errMsg && err.errMsg.includes('cancel')) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 微信小程序支付处理（差价支付专用）\n\t\thandleMiniProgramPayForDiff(obj, diffItem) {\n\t\t\tconst paymentParams = {\n\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string\n\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\tsignType: 'MD5',\n\t\t\t\tpaySign: obj.sign\n\t\t\t};\n\t\t\tconsole.log(JSON.stringify(paymentParams));\n\t\t\tuni.requestPayment({\n\t\t\t\t\"provider\": 'wxpay',\n\t\t\t\ttimeStamp: String(obj.timestamp),\n\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\tsignType: \"MD5\",\n\t\t\t\tpaySign: obj.sign,\n\t\t\t\tappId: obj.appId,\n\t\t\t\tsuccess: (res1) => {\n\t\t\t\t\t// 支付成功回调\n\t\t\t\t\tconsole.log('支付成功', res1);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t\tthis.page = 1;\n\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\t// 支付失败回调\n\t\t\t\t\tconsole.error('requestPayment fail object:', err);\n\t\t\t\t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\n\t\t\t\t\tif (err.errMsg.includes('fail cancel')) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tconsole.error('支付失败', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付失败请检查网络',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t})\n\t\t\t\t},\n\t\t\t})\n\t\t},\n\t\t// 获取差价申请状态文本\n\t\tgetDiffStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t'-1': '已取消',\n\t\t\t\t0: '待确认',\n\t\t\t\t1: '已确认待支付',\n\t\t\t\t2: '已支付',\n\t\t\t\t3: '已拒绝'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知状态';\n\t\t},\n\n\t\t// 去支付差价\n\t\tpayDiffPrice(diffItem) {\n\t\t\tthis.$api.service.payDiffPrice({\n\t\t\t\tid: diffItem.id,\n\t\t\t\ttype: 1\n\t\t\t}).then(res => {\n\t\t\t\tif (res.code === \"200\") {\n\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tlet obj = res.data\n\t\t\t\t\tlet packageStr = \"prepay_id=\" + obj.prepayId;\n\t\t\t\t\tconsole.log(String(packageStr))\n\t\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\t\tconsole.log(packageStr)\n\t\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\t\tconsole.log(String(obj.timestamp))\n\t\t\t\t\tconsole.log(obj.sign)\n\n\t\t\t\t\t// 获取当前平台\n\t\t\t\t\tconst platform = this.getCurrentPlatform();\n\t\t\t\t\tconsole.log('当前平台:', platform);\n\n\t\t\t\t\t// 根据平台选择不同的支付方式\n\t\t\t\t\tif (platform === 'app-plus') {\n\t\t\t\t\t\t// APP环境使用微信支付\n\t\t\t\t\t\tconsole.log('APP环境，使用微信支付');\n\t\t\t\t\t\tthis.handleAppWechatPayForDiff(obj, diffItem);\n\t\t\t\t\t} else if (platform === 'mp-weixin') {\n\t\t\t\t\t\t// 微信小程序环境保持原有逻辑\n\t\t\t\t\t\tconsole.log('微信小程序环境，使用小程序支付');\n\t\t\t\t\t\tthis.handleMiniProgramPayForDiff(obj, diffItem);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 其他环境（H5等）\n\t\t\t\t\t\tconsole.log('其他环境，使用默认支付方式');\n\t\t\t\t\t\tthis.handleMiniProgramPayForDiff(obj, diffItem);\n\t\t\t\t\t}\n\n\n\n\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '支付失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '支付失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('Error in payDiffPrice:', err);\n\t\t\t});\n\t\t},\n\n\t\t// 确认差价\n\t\tconfirmDiffPrice(diffItem) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认差价',\n\t\t\t\tcontent: `确定要同意差价金额 ￥${diffItem.diffAmount} 吗？`,\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.updateDiffStatus(diffItem, 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 拒绝差价\n\t\trejectDiffPrice(diffItem) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '拒绝差价',\n\t\t\t\tcontent: '请输入拒绝原因',\n\t\t\t\teditable: true,\n\t\t\t\tplaceholderText: '请输入拒绝原因',\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tconst rejectText = res.content || '';\n\t\t\t\t\t\tif (!rejectText.trim()) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '请输入拒绝原因',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.updateDiffStatus(diffItem, 2, rejectText);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 更新差价状态\n\t\tupdateDiffStatus(diffItem, status, text = '') {\n\t\t\tconst params = {\n\t\t\t\tid: diffItem.id,\n\t\t\t\tstatus: status\n\t\t\t};\n\t\t\tif (status === 2 && text) {\n\t\t\t\tparams.text = text;\n\t\t\t}\n\n\t\t\tthis.$api.service.updateDiffStatus(params).then(res => {\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: status === 1 ? '已同意' : '已拒绝',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.page = 1;\n\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '操作失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('Error in updateDiffStatus:', err);\n\t\t\t});\n\t\t},\n\n\t\t// 预览配件图片\n\t\tpreviewImage(imageUrl) {\n\t\t\tuni.previewImage({\n\t\t\t\turls: [imageUrl],\n\t\t\t\tcurrent: imageUrl\n\t\t\t});\n\t\t},\n\n\n\n\n\n\t\thuodongclick() {\n\t\t\tuni.showToast({\n\t\t\t\ticon: 'none',\n\t\t\t\ttitle: '耐心等待师傅上门，有问题联系客服'\n\t\t\t});\n\t\t},\n\t\tgohuodongevaluate(item) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/user/addevaluate?id=${item.id}&goodsId=${item.goodsId}&huodong=${1}`\n\t\t\t});\n\t\t},\n\t\tgozhifu(item) {\n\t\t\tconsole.log(item)\n\t\t\tthis.currentItem = item;\n\t\t\tthis.showPaymentModal = true;\n\t\t},\n\t\tconfirmPayment() {\n\t\t\tthis.showPaymentModal = false;\n\t\t\tif (this.currentItem) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/user/Cashier?id=${this.currentItem.id}&price=${this.currentItem.payPrice}&type=${this.currentItem.payType}&goodsId=${this.currentItem.goodsId}`\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\thuodongwanchengclick(item) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认完成',\n\t\t\t\tcontent: '师傅是否已完成订单？',\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.$api.service.huodongqueding({\n\t\t\t\t\t\t\tid: item.id\n\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\ttitle: '订单完成'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tthis.page = 1;\n\t\t\t\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\ttitle: err.msg || '操作失败'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tconsole.error('Cancel Error:', err);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\thuodongquxiaos(item) {\n\t\t\tthis.showCancel = false;\n\t\t\tthis.$api.service.huodongquxiao({\n\t\t\t\tid: item.id\n\t\t\t}).then(res => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '取消成功'\n\t\t\t\t});\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.orderList = [];\n\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '取消失败'\n\t\t\t\t});\n\t\t\t\tconsole.error('Cancel Error:', err);\n\t\t\t});\n\t\t},\n\t\tdingyue() {\n\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\tconst requiredTmplId = '';\n\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst otherTmplIds = allTmplIds.filter(id => id !== requiredTmplId);\n\t\t\tconst shuffled = otherTmplIds.sort(() => 0.5 - Math.random());\n\t\t\tconst selectedTmplIds = [requiredTmplId, ...shuffled.slice(0, 2)];\n\t\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\t\ttemplateId: id,\n\t\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t\t}));\n\t\t\tuni.requestSubscribeMessage({\n\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: (err) => { }\n\t\t\t});\n\t\t},\n\t\tupdateHighlight(options) {\n\t\t\tconst userId = uni.getStorageSync('userId');\n\t\t\tif (!userId) {\n\t\t\t\tconsole.log('No userId, skipping updateHighlight');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.$api.service.updataHighlight({\n\t\t\t\tuserId: userId,\n\t\t\t\trole: 1,\n\t\t\t\tpayType: options.tab\n\t\t\t}).then(res => {\n\t\t\t\tconsole.log('updateHighlight response:', res);\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('updateHighlight error:', err);\n\t\t\t});\n\t\t},\n\t\tgoevaluate(item) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/user/addevaluate?id=${item.id}&goodsId=${item.goodsId}`\n\t\t\t});\n\t\t},\n\t\tapplyT(item) {\n\t\t\tthis.currentItem = item;\n\t\t\tthis.showRefundModal = true;\n\t\t},\n\t\tconfirmRefund() {\n\t\t\tthis.showRefundModal = false;\n\t\t\tif (this.currentItem) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/user/tuicause?order_id=${this.currentItem.id}`\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t// New method to view refund details\n\t\tviewRefundDetails(item) {\n\t\t\tthis.$api.service.refundProgress({\n\t\t\t\tid: item.id\n\t\t\t}).then(res => {\n\t\t\t\tif (res.code === \"200\" && res.data) {\n\t\t\t\t\tthis.refundDetails = res.data;\n\t\t\t\t\tthis.showRefundDetailsModal = true;\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg || '获取退款详情失败'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: err.msg || '获取退款详情失败'\n\t\t\t\t});\n\t\t\t\tconsole.error('Refund details error:', err);\n\t\t\t});\n\t\t},\n\t\t// Helper to format refund status text\n\t\trefundStatusText(status) {\n\t\t\tswitch (status) {\n\t\t\t\tcase 1:\n\t\t\t\t\treturn '审核中';\n\t\t\t\tcase 2:\n\t\t\t\t\treturn '已退款';\n\t\t\t\tcase 3:\n\t\t\t\t\treturn '驳回';\n\t\t\t\tdefault:\n\t\t\t\t\treturn '未知状态';\n\t\t\t}\n\t\t},\n\t\tgoChoose(item) {\n\t\t\tconsole.log(item)\n\t\t\tthis.$store.commit('changeOrderInfo', item);\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/user/choose_master'\n\t\t\t});\n\t\t},\n\t\tconfirmorder(item) {\n\t\t\tthis.id = item.id;\n\t\t\tthis.showConfirm = true;\n\t\t},\n\t\tconfirmconfirm() {\n\t\t\tthis.showConfirm = false;\n\t\t\tthis.$api.service.confirmOrder({\n\t\t\t\torderId: this.id\n\t\t\t}).then(res => {\n\t\t\t\tif (res.code === '-1') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\ttitle: '操作成功'\n\t\t\t\t\t});\n\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\turl: `/user/order_list?tab=7`\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '操作失败'\n\t\t\t\t});\n\t\t\t\tconsole.error('Confirm Error:', err);\n\t\t\t});\n\t\t},\n\t\tconfirmCancel() {\n\t\t\tthis.showCancel = false;\n\t\t\tthis.$api.service.cancelOrder({\n\t\t\t\tid: this.id\n\t\t\t}).then(res => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '取消成功'\n\t\t\t\t});\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.orderList = [];\n\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '取消失败'\n\t\t\t\t});\n\t\t\t\tconsole.error('Cancel Error:', err);\n\t\t\t});\n\t\t},\n\t\tcancelorder(item) {\n\t\t\tthis.id = item.id;\n\t\t\tthis.showCancel = true;\n\t\t},\n\t\tgoUrl(e) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: e\n\t\t\t});\n\t\t},\n\t\thandleOrderClick(item) {\n\t\t\t// 如果是已完成订单(payType === 7)，跳转到完成订单详情页\n\t\t\tif (item.payType === 7) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/user/order_detail_wancheng/order_detail_wancheng?id=${item.id}`\n\t\t\t\t});\n\t\t\t} else if (item.type !== 5) {\n\t\t\t\t// 其他情况跳转到普通订单详情页\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/user/order_details?id=${item.id}`\n\t\t\t\t});\n\t\t\t}\n\t\t\t// 如果是type === 5，不执行任何跳转\n\t\t},\n\t\tgetList(nval) {\n\t\t\tconst payType = nval !== undefined ? nval : this.currentIndex;\n\t\t\tconsole.log('getList called with payType:', payType, 'page:', this.page);\n\n\t\t\t// 显示加载状态\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中...'\n\t\t\t});\n\n\t\t\tif (payType === 0) {\n\t\t\t\treturn Promise.all([\n\t\t\t\t\tthis.$api.service.huodongorder(),\n\t\t\t\t\tthis.$api.service.userOrder({\n\t\t\t\t\t\tpayType: 0,\n\t\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\t\tpageSize: 10\n\t\t\t\t\t})\n\t\t\t\t]).then(([huodongRes, userOrderRes]) => {\n\t\t\t\t\tconsole.log('活动订单响应:', huodongRes);\n\t\t\t\t\tconsole.log('用户订单响应:', userOrderRes);\n\n\t\t\t\t\tconst huodongList = (huodongRes.code === \"200\" && huodongRes.data) ?\n\t\t\t\t\t\t(Array.isArray(huodongRes.data) ? huodongRes.data.filter(item => item != null) : [\n\t\t\t\t\t\t\thuodongRes.data\n\t\t\t\t\t\t]) : [];\n\t\t\t\t\tthis.huodonglist = huodongList;\n\n\t\t\t\t\tconst userList = Array.isArray(userOrderRes.data.list) ? userOrderRes.data.list : [];\n\t\t\t\t\tconst normalizedUserList = userList.map(item => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\tpayType: parseInt(item.payType)\n\t\t\t\t\t}));\n\n\t\t\t\t\tconst combinedList = [...huodongList, ...normalizedUserList];\n\t\t\t\t\tconsole.log('合并后的订单列表:', combinedList);\n\n\t\t\t\t\t// 使用 Vue.set 确保响应式更新\n\t\t\t\t\tthis.$set(this, 'orderList', combinedList);\n\t\t\t\t\tthis.status = userList.length < 10 ? 'nomore' : 'loadmore';\n\n\t\t\t\t\tconsole.log('订单数据加载成功，总数:', combinedList.length);\n\t\t\t\t\tconsole.log('当前 orderList 长度:', this.orderList.length);\n\n\t\t\t\t\tuni.hideLoading();\n\n\t\t\t\t\t// 强制触发视图更新\n\t\t\t\t\tthis.$forceUpdate();\n\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '获取订单失败'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('API Error:', err);\n\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\tthis.huodonglist = [];\n\t\t\t\t\treturn Promise.reject(err);\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tthis.huodonglist = [];\n\t\t\t\treturn this.$api.service.userOrder({\n\t\t\t\t\tpayType,\n\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\tpageSize: 10\n\t\t\t\t}).then(ress => {\n\t\t\t\t\tconsole.log('用户订单响应:', ress);\n\t\t\t\t\tlet res = ress.data;\n\t\t\t\t\tconst list = Array.isArray(res.list) ? res.list : [];\n\t\t\t\t\tconst normalizedList = list.map(item => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\tpayType: parseInt(item.payType)\n\t\t\t\t\t}));\n\n\t\t\t\t\tconsole.log('标准化后的订单列表:', normalizedList);\n\n\t\t\t\t\t// 使用 Vue.set 确保响应式更新\n\t\t\t\t\tthis.$set(this, 'orderList', normalizedList);\n\t\t\t\t\tthis.status = list.length < 10 ? 'nomore' : 'loadmore';\n\n\t\t\t\t\tconsole.log('订单数据加载成功，总数:', normalizedList.length);\n\t\t\t\t\tconsole.log('当前 orderList 长度:', this.orderList.length);\n\n\t\t\t\t\tuni.hideLoading();\n\n\t\t\t\t\t// 强制触发视图更新\n\t\t\t\t\tthis.$forceUpdate();\n\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '获取订单失败'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('API Error:', err);\n\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\treturn Promise.reject(err);\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\thandleHeader(item) {\n\t\t\tthis.currentIndex = item.value;\n\t\t},\n\t\tgetcommissionRatio() {\n\t\t\tthis.$api.service.commissionRatio().then(res => {\n\t\t\t\tthis.jiaNum = res.data;\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('getcommissionRatio Error:', err);\n\t\t\t});\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tconsole.log('order_list onLoad, options:', options);\n\t\tif (options.from && options.from === 'tiaozhuan') {\n\t\t\tthis.isFromTiaozhuan = true;\n\t\t\tconsole.log('来源是跳转页，返回时将执行默认返回');\n\t\t} else if (options.from && options.from === 'cart_play') {\n\t\t\tthis.isFromCartPlay = true;\n\t\t\tconsole.log('来源是购物车下单页，返回时将跳转到订单页面');\n\t\t} else {\n\t\t\tthis.isFromTiaozhuan = false;\n\t\t\tthis.isFromCartPlay = false;\n\t\t\tconsole.log('来源是其他页面，返回时将跳转到\"我的\"页面');\n\t\t}\n\n\t\t// 初始化基础数据\n\t\tthis.$api.service.remind().then(res => {\n\t\t\tif (res.code === \"200\") {\n\t\t\t\tthis.reminddata = res.data.cancelRemind;\n\t\t\t\tthis.paymentRemind = res.data.paymentRemind;\n\t\t\t}\n\t\t}).catch(err => {\n\t\t\tconsole.error('获取提醒信息失败:', err);\n\t\t});\n\n\t\tthis.updateHighlight(options);\n\t\tthis.currentIndex = options.tab ? parseInt(options.tab) : 0;\n\t\tthis.page = 1; // 重置页码\n\t\tthis.orderList = []; // 清空现有数据\n\t\tthis.status = 'loadmore'; // 重置状态\n\n\t\t// 延迟加载数据，确保页面完全初始化\n\t\tthis.$nextTick(() => {\n\t\t\tsetTimeout(() => {\n\t\t\t\tconsole.log('开始加载订单数据...');\n\t\t\t\tthis.getList(this.currentIndex).then(() => {\n\t\t\t\t\tconsole.log('订单数据加载完成，当前订单数量:', this.orderList.length);\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('订单数据加载失败:', err);\n\t\t\t\t});\n\t\t\t}, 100);\n\t\t});\n\n\t\tthis.getcommissionRatio();\n\t},\n\tonShow() {\n\t\tconsole.log('order_list onShow 触发');\n\n\t\tuni.$on('cancelOr', () => {\n\t\t\tthis.currentIndex = 0;\n\t\t\tthis.page = 1;\n\t\t\tthis.getList();\n\t\t});\n\n\t\t// 获取当前页面信息\n\t\tconst pages = getCurrentPages();\n\t\tconst currentPage = pages[pages.length - 1];\n\t\tconst options = currentPage.options || {};\n\n\t\tconsole.log('order_list onShow, options:', options);\n\t\tconsole.log('当前订单数量:', this.orderList.length);\n\n\t\t// 如果是从 tiaozhuan 页面跳转过来且数据为空，强制刷新\n\t\tif (options.from === 'tiaozhuan' && this.orderList.length === 0) {\n\t\t\tconsole.log('从跳转页面过来且数据为空，强制刷新数据');\n\t\t\tthis.page = 1;\n\t\t\tthis.orderList = [];\n\t\t\tthis.status = 'loadmore';\n\t\t\tthis.getList(this.currentIndex).then(() => {\n\t\t\t\tconsole.log('强制刷新完成，订单数量:', this.orderList.length);\n\t\t\t});\n\t\t\treturn;\n\t\t}\n\n\t\t// 检查是否是从其他页面跳转过来的（有特殊参数）\n\t\tif (options.tab !== undefined || options.refresh) {\n\t\t\t// 如果有tab参数或refresh参数，需要刷新数据\n\t\t\tif (options.tab !== undefined) {\n\t\t\t\tthis.currentIndex = parseInt(options.tab);\n\t\t\t}\n\t\t\tthis.page = 1;\n\t\t\tthis.orderList = []; // 清空现有数据\n\t\t\tthis.status = 'loadmore';\n\t\t\tthis.getList(this.currentIndex);\n\t\t}\n\t\t// 如果没有特殊参数，说明是正常的页面显示，不需要重复加载数据\n\t},\n\tonBackPress() {\n\t\tif (this.isFromCartPlay) {\n\t\t\t// 如果是从购物车下单页面来的，返回到订单页面并刷新\n\t\t\tuni.redirectTo({\n\t\t\t\turl: '/pages/order?refresh=1'\n\t\t\t});\n\t\t} else {\n\t\t\t// 其他情况返回到我的页面\n\t\t\tuni.redirectTo({\n\t\t\t\turl: '/pages/mine'\n\t\t\t});\n\t\t}\n\t\treturn true;\n\t},\n\tonUnload() {\n\t\tif (this.isFromTiaozhuan) {\n\t\t\tuni.redirectTo({\n\t\t\t\turl: '/pages/mine'\n\t\t\t});\n\t\t} else if (this.isFromCartPlay) {\n\t\t\t// 如果是从购物车下单页面来的，页面卸载时跳转到订单页面并刷新\n\t\t\tuni.redirectTo({\n\t\t\t\turl: '/pages/order?refresh=1'\n\t\t\t});\n\t\t}\n\t},\n\twatch: {\n\t\tcurrentIndex(nval) {\n\t\t\tthis.page = 1;\n\t\t\tthis.orderList = [];\n\t\t\tthis.status = 'loadmore';\n\t\t\tthis.getList(nval);\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tbackground-color: #F8F8F8;\n\tmin-height: 100vh;\n\tpadding-top: 100rpx;\n\n\t.header {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\twidth: 750rpx;\n\t\theight: 100rpx;\n\t\tbackground: #FFFFFF;\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\talign-items: center;\n\t\tz-index: 99;\n\n\t\t.header_item {\n\t\t\tmax-width: 90rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #999999;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\tflex-wrap: wrap;\n\t\t\twhite-space: nowrap;\n\n\t\t\t.blue {\n\t\t\t\tmargin-top: 8rpx;\n\t\t\t\twidth: 38rpx;\n\t\t\t\theight: 6rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 4rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.main {\n\t\tpadding: 40rpx 30rpx;\n\n\t\t.main_item {\n\t\t\twidth: 690rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 24rpx;\n\t\t\tpadding: 28rpx 36rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tbox-sizing: border-box;\n\n\t\t\t.head {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\n\t\t\t\t.no {\n\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.mid {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.lef {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tflex: 1;\n\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\timage {\n\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tmargin-left: 30rpx;\n\t\t\t\t\t\tmax-width: 350rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.righ {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\ttext-align: right;\n\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.bot {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.qzf,\n\t\t\t\t.qxdd,\n\t\t\t\t.lxsf,\n\t\t\t\t.qrwc,\n\t\t\t\t.qpl {\n\t\t\t\t\twidth: 148rpx;\n\t\t\t\t\theight: 48rpx;\n\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\tborder-radius: 50rpx;\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tline-height: 48rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\n\t\t\t\t.qzf,\n\t\t\t\t.qrwc,\n\t\t\t\t.qpl {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\n\t\t\t\t.qxdd,\n\t\t\t\t.lxsf {\n\t\t\t\t\tbackground: #FFFFFF;\n\t\t\t\t\tborder: 2rpx solid #2E80FE;\n\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 子订单样式\n\t\t\t.sub_orders {\n\t\t\t\tmargin-top: 30rpx;\n\t\t\t\tpadding-top: 20rpx;\n\t\t\t\tborder-top: 1px solid #f0f0f0;\n\n\t\t\t\t.sub_title {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\n\t\t\t\t.sub_item {\n\t\t\t\t\tbackground: #f8f9fa;\n\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\tpadding: 20rpx;\n\t\t\t\t\tmargin-bottom: 15rpx;\n\n\t\t\t\t\t.sub_head {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tmargin-bottom: 15rpx;\n\n\t\t\t\t\t\t.sub_no {\n\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\tmax-width: 400rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sub_status {\n\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sub_content {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\talign-items: flex-end;\n\n\t\t\t\t\t\t.sub_info {\n\t\t\t\t\t\t\tflex: 1;\n\n\t\t\t\t\t\t\t.sub_amount {\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.sub_reason {\n\t\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t\t\t\tmax-width: 300rpx;\n\t\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.sub_time {\n\t\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sub_actions {\n\t\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tgap: 10rpx;\n\n\t\t\t\t\t\t\t.sub_qzf {\n\t\t\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\t\t\theight: 40rpx;\n\t\t\t\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\t\t\t\tfont-size: 18rpx;\n\t\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\t\tline-height: 40rpx;\n\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\tcolor: #fff;\n\n\t\t\t\t\t\t\t\t&.sub_reject {\n\t\t\t\t\t\t\t\t\tbackground: #ff6b6b;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.main_item_already {\n\t\t\tpadding: 28rpx 36rpx;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-radius: 24rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tbox-sizing: border-box;\n\n\t\t\t.qxdd {\n\t\t\t\twidth: 148rpx;\n\t\t\t\theight: 48rpx;\n\t\t\t\tbackground: #FFFFFF;\n\t\t\t\tborder-radius: 50rpx;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tline-height: 48rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tborder: 2rpx solid #2E80FE;\n\t\t\t\tcolor: #2E80FE;\n\t\t\t}\n\n\t\t\t.no {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\t\t\t\tmax-width: 500rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t}\n\n\t\t\t.mid {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.lef {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tflex: 1;\n\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\timage {\n\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tmargin-left: 30rpx;\n\t\t\t\t\t\tmax-width: 350rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.bot {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\t\t\t}\n\n\t\t\t.shifu {\n\t\t\t\tmargin-top: 20rpx;\n\n\t\t\t\t.shifu_item {\n\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-right: 20rpx;\n\n\t\t\t\t\timage {\n\t\t\t\t\t\twidth: 90rpx;\n\t\t\t\t\t\theight: 90rpx;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t}\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.tips {\n\t\t\t\tmargin-top: 10rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #2E80FE;\n\t\t\t}\n\n\t\t\t.title {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #333333;\n\t\t\t}\n\n\t\t\t.ok {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #2E80FE;\n\t\t\t\tmargin-top: 10rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.modal-content-red {\n\tcolor: #F60100;\n\tpadding: 10rpx 30rpx;\n\ttext-align: center;\n}\n\n.modal-content-details {\n\tpadding: 20rpx;\n\tfont-size: 28rpx;\n\tcolor: #333333;\n\n\t.detail-item {\n\t\tdisplay: flex;\n\t\tmargin-bottom: 10rpx;\n\n\t\t.label {\n\t\t\tfont-weight: bold;\n\t\t\twidth: 150rpx;\n\t\t\tflex-shrink: 0;\n\t\t}\n\n\t\t.value {\n\t\t\tflex-grow: 1;\n\t\t}\n\t}\n}\n</style>\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=style&index=0&id=1ee6bdd6&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=style&index=0&id=1ee6bdd6&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755734336884\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}