<view class="pages-mine"><view class="header"><view class="header-content"><view class="avatar_view"><image class="avatar" mode="aspectFill" src="{{displayUserInfo.avatarUrl||'/static/mine/default_user.png'}}"></image></view><view class="user-info"><block wx:if="{{!isLoggedIn}}"><view><button class="{{[(isLoading)?'loading':'']}}" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['showLoginPopup',['$event']]]]]}}" bindtap="__e">{{''+(isLoading?'登录中...':'用户登录')+''}}</button></view></block><block wx:else><view class="user-info-logged"><view class="nickname">{{''+displayUserInfo.nickName+''}}</view><block wx:if="{{displayUserInfo.phone}}"><view class="phone-number">{{displayUserInfo.phone}}</view></block><block wx:else><view class="bind-phone-container"><button class="bind-phone-btn" disabled="{{isBindingPhone}}" data-event-opts="{{[['tap',[['showBindPhonePopup',['$event']]]]]}}" bindtap="__e">{{''+(isBindingPhone?'绑定中...':'绑定手机号')+''}}</button></view></block><view class="status-info-container"><view data-event-opts="{{[['tap',[['handleStatusClick',['$event']]]]]}}" class="{{['status-badge',statusBadgeClass]}}" bindtap="__e">{{''+statusText+"\n\t\t\t\t\t\t\t"+labelName+''}}</view><block wx:if="{{isLoggedIn&&shifustatus!==-1}}"><view class="master-info"><view data-event-opts="{{[['tap',[['gocredit',['$event']]]]]}}" class="credit-info" bindtap="__e">{{"信用分: "+credit}}</view><view class="star-info">{{starRating}}</view></view></block></view></view></block></view><view data-event-opts="{{[['tap',[['navigateTo',['../user/userProfile']]]]]}}" class="settings" bindtap="__e"><view class="iconfont icon-xitong text-bold _i"></view></view></view></view><view class="mine-menu-list box-shadow fill-base box1"><view class="menu-title flex-between pl-lg pr-md b-1px-b"><view class="f-paragraph c-title text-bold">我的订单</view></view><view class="flex-warp pt-lg pb-lg"><block wx:for="{{orderList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navigateTo',['$0'],[[['orderList','',index,'url']]]]]]]}}" class="order-item" bindtap="__e"><view class="icon-container"><u-icon vue-id="{{'fe5e5c8c-1-'+index}}" name="{{item.icon}}" color="#448cfb" size="28" bind:__l="__l"></u-icon><block wx:if="{{item.count>0}}"><view class="number-circle">{{item.count}}</view></block></view><view class="mt-sm">{{item.text}}</view></view></block></view></view><view class="mine-menu-list box-shadow fill-base"><view class="menu-title flex-between pl-lg pr-md b-1px-b"><view class="f-paragraph c-title text-bold">常用功能</view></view><view class="flex-warp pt-lg pb-lg"><block wx:for="{{orderList3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleNavigate',['$0'],[[['orderList3','',index,'url']]]]]]]}}" class="order-item" bindtap="__e"><u-icon vue-id="{{'fe5e5c8c-2-'+index}}" name="{{item.icon}}" color="#448cfb" size="28" bind:__l="__l"></u-icon><view class="mt-sm">{{item.text}}</view></view></block></view></view><view class="mine-menu-list box-shadow fill-base"><view class="menu-title flex-between pl-lg pr-md b-1px-b"><view class="f-paragraph c-title text-bold">其他功能</view></view><view class="flex-warp pt-lg pb-lg"><view data-event-opts="{{[['tap',[['handleNavigate',['/shifu/skills']]]]]}}" class="order-item" bindtap="__e"><u-icon vue-id="fe5e5c8c-3" name="plus-square-fill" color="#448cfb" size="28" bind:__l="__l"></u-icon><view class="mt-sm">技能标签</view></view><view data-event-opts="{{[['tap',[['handleNavigate',['/shifu/Professiona']]]]]}}" class="order-item" bindtap="__e"><u-icon vue-id="fe5e5c8c-4" name="order" color="#448cfb" size="28" bind:__l="__l"></u-icon><view class="mt-sm">技能证书</view></view><view data-event-opts="{{[['tap',[['handleNavigate',['/shifu/coreWallet']]]]]}}" class="order-item" bindtap="__e"><u-icon vue-id="fe5e5c8c-5" name="rmb-circle-fill" color="#448cfb" size="28" bind:__l="__l"></u-icon><view class="mt-sm" style="color:#448cfb;">提现管理</view></view><view data-event-opts="{{[['tap',[['handleNavigate',['/user/promotion']]]]]}}" class="order-item" bindtap="__e"><u-icon vue-id="fe5e5c8c-6" name="red-packet-fill" color="#E41F19" size="28" bind:__l="__l"></u-icon><view class="mt-sm" style="color:#E41F19;">邀请有礼</view></view></view></view><view class="spacer"></view><view class="mine-tool-grid fill-base"><view class="grid-container"><block wx:for="{{toolList2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleNavigate',['$0'],[[['toolList2','',index,'url']]]]]]]}}" class="grid-item" bindtap="__e"><view class="grid-icon-container"><u-icon vue-id="{{'fe5e5c8c-7-'+index}}" name="{{item.icon}}" color="{{item.iconColor}}" size="28" bind:__l="__l"></u-icon></view><view class="grid-text">{{item.text}}</view></view></block><view class="grid-item"><button class="contact-btn-wrapper" open-type="contact" bindcontact="handleContact" session-from="sessionFrom"><view class="grid-icon-container switch-identity"><u-icon vue-id="fe5e5c8c-8" name="server-man" color="#448cfb" size="28" bind:__l="__l"></u-icon></view><view class="grid-text">客服</view></button></view></view></view><block wx:if="{{loginPopupVisible}}"><view data-event-opts="{{[['tap',[['hideLoginPopup',['$event']]]]]}}" class="login-popup-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="login-popup" catchtap="__e"><view data-event-opts="{{[['tap',[['hideLoginPopup',['$event']]]]]}}" class="close-btn" bindtap="__e"><view class="iconfont icon-close _i"></view></view><view class="popup-content"><view class="welcome-title">欢迎登录今师傅</view><view class="welcome-subtitle">登录后即可享受完整服务</view><view class="agreement-section"><view data-event-opts="{{[['tap',[['toggleAgreement',['$event']]]]]}}" class="checkbox-container" bindtap="__e"><view class="{{['checkbox',(agreedToTerms)?'checked':'']}}"><block wx:if="{{agreedToTerms}}"><view class="iconfont icon-check _i">✓</view></block></view><view class="agreement-text">我已阅读并同意<text data-event-opts="{{[['tap',[['navigateToAgreement',['service']]]]]}}" class="link" catchtap="__e">《今师傅服务协议》</text><text data-event-opts="{{[['tap',[['navigateToAgreement',['privacy']]]]]}}" class="link" catchtap="__e">《隐私政策》</text></view></view></view><button class="{{['phone-login-btn',(!agreedToTerms||isLoading)?'disabled':'']}}" disabled="{{!agreedToTerms||isLoading}}" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['onGetPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">{{''+(isLoading?'登录中...':'手机号快捷登录')+''}}</button></view></view></view></block><block wx:if="{{bindPhonePopupVisible}}"><view data-event-opts="{{[['tap',[['hideBindPhonePopup',['$event']]]]]}}" class="login-popup-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="login-popup" catchtap="__e"><view data-event-opts="{{[['tap',[['hideBindPhonePopup',['$event']]]]]}}" class="close-btn" bindtap="__e"><view class="iconfont icon-close _i"></view></view><view class="popup-content"><view class="welcome-title">绑定手机号</view><view class="welcome-subtitle">绑定手机号后可享受完整服务</view><view class="input-group"><view class="input-item"><view class="input-icon"><u-icon vue-id="fe5e5c8c-9" name="phone" color="#3b82f6" size="18" bind:__l="__l"></u-icon></view><input class="input-field" type="number" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['bindPhoneForm']]]]]}}" value="{{bindPhoneForm.phone}}" bindinput="__e"/></view><view class="input-item"><view class="input-icon"><u-icon vue-id="fe5e5c8c-10" name="chat" color="#3b82f6" size="18" bind:__l="__l"></u-icon></view><input class="input-field" type="number" placeholder="请输入验证码" maxlength="6" data-event-opts="{{[['input',[['__set_model',['$0','code','$event',[]],['bindPhoneForm']]]]]}}" value="{{bindPhoneForm.code}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendBindPhoneSmsCode',['$event']]]]]}}" class="{{['sms-btn',(bindPhoneSmsCountdown>0)?'disabled':'']}}" bindtap="__e">{{''+(bindPhoneSmsCountdown>0?bindPhoneSmsCountdown+'s':'获取验证码')+''}}</view></view></view><button class="{{['phone-login-btn',(!canBindPhone||isBindingPhone)?'disabled':'']}}" disabled="{{!canBindPhone||isBindingPhone}}" data-event-opts="{{[['tap',[['handleBindPhone',['$event']]]]]}}" bindtap="__e">{{''+(isBindingPhone?'绑定中...':'绑定手机号')+''}}</button></view></view></view></block><block wx:if="{{rejectReasonVisible}}"><view data-event-opts="{{[['tap',[['hideRejectReason',['$event']]]]]}}" class="reject-reason-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="reject-reason-popup" catchtap="__e"><view data-event-opts="{{[['tap',[['hideRejectReason',['$event']]]]]}}" class="close-btn" bindtap="__e"><view class="iconfont icon-close _i"></view></view><view class="popup-content"><view class="popup-title">审核驳回原因</view><view class="reject-reason-text">{{shText||'暂无驳回原因说明'}}</view><button data-event-opts="{{[['tap',[['hideRejectReason',['$event']]]]]}}" class="confirm-btn" bindtap="__e">我知道了</button></view></view></view></block><tabbar vue-id="fe5e5c8c-11" cur="1" bind:__l="__l"></tabbar></view>